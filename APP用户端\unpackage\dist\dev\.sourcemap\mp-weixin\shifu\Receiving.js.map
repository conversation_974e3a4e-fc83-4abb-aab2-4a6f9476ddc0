{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?4ea9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?968f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?491f", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?4d91", "uni-app:///shifu/Receiving.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?73cf", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/shifu/Receiving.vue?849a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "orderData", "showDing<PERSON><PERSON>", "showCate", "infodata", "tmplIds", "status", "id", "shifuId", "list", "show", "confirmshow", "masterModalShow", "detailModalShow", "content", "input", "area_id", "limit", "page", "bannerList", "configInfo", "getconfigs", "list1", "lng", "shi<PERSON><PERSON><PERSON>us", "msg", "lat", "cateList", "currentCateId", "currentCateName", "copyCateList", "province", "city", "district", "isPageLoaded", "selectedItem", "computed", "configInfos", "refreshReceiving", "methods", "dingyue", "console", "templateId", "templateCategoryId", "uni", "success", "title", "cancelText", "confirmText", "confirmColor", "withSubscriptions", "selectedTmplIds", "fail", "validateInput", "value", "parts", "reset", "textsss", "closeCate", "setTimeout", "chooseCate", "asyncChange", "url", "confirmSubscription", "icon", "cancelSubscription", "requestSubscription", "checkSubscriptionStatus", "subscriptions", "selectClick", "cate", "parentId", "res", "count", "getCate", "seeDetail", "confirmDetail", "getList", "location", "longitude", "latitude", "type", "geoRes", "pageNum", "pageSize", "handleReceive", "close", "confirmRe", "order_id", "duration", "goToSettle", "getServiceInfo", "city_id", "onReachBottom", "confirmBao", "orderId", "price", "initializePage", "systemInfo", "onLoad", "onPullDownRefresh", "onShow"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,gRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAw1B,CAAgB,w2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC+F52B;AAGA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,UACA,IACA,IACA,8CACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QAAA;QACAxB;QACAyB;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA,GAEA;;EAEAC;IACAC;MAAA;MACAC;MACA;MACA;QACAA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;QAAA;MAAA;MACA;MACAA;MACA;QAAA;UACAC;UACAC;QACA;MAAA;MACAC;QACAvC;QACAwC;UACAJ;UACA;UACA;YAAA;UAAA;UACA;UACA;YACAG;cACAE;cACAhC;cACAiC;cACAC;cACAC;cACAJ;gBACAD;gBACA;kBACAA;oBACAM;kBACA;gBACA;kBACAN;gBACA;cACA;YACA;UACA;UACA;UACAO;YACAV;YACA;cACA;cACA;gBACA;kBACA;gBACA;cACA;gBACA;cACA;cACAA;YACA;UACA;UACAA;QACA;QACAW;UACAX;QACA;MACA;IACA;IAGA;IACAY;MACA;;MAEA;MACAC;;MAEA;MACAA;;MAEA;MACA;MACA;QACA;QACAA;MACA;MAEA;QACA;QACA;UACAC;UACAD;QACA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;IACA;IAEAE;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACAb;UACAvC;UACAwC;YACAJ;UACA;UACAW;YACAX;UACA;QACA;MACA;IACA;IACAiB;MAAA;MACA;MACAC;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACAjB;QACAE;QACAhC;QACA+B;UACAD;YACAkB;UACA;QACA;QACAV;UACA;UACA;UACA;UACA;UACA;QAAA;MAEA;IACA;IACAW;MAAA;MACAnB;QACAM;QACAL;UACAJ;UACAG;YACAM;YACAL;cACA;cACAJ;cACA;gBACA;cACA;gBACA;gBACAG;kBACAE;kBACAkB;gBACA;gBACA;cACA;YACA;YACAZ;cACAX;cACA;cACAG;gBACAE;gBACAkB;cACA;YACA;UACA;QACA;QACAZ;UACAX;UACA;UACAG;YACAE;YACAkB;UACA;QACA;MACA;IACA;IACAC;MACA;MACArB;QACAE;QACAkB;MACA;IACA;IACAE;MAAA;MACA;MACAzB;MACA;QACAG;UACAvC;UACAwC;YACAJ;YACA;cAAA;YAAA;YACA;cACA;cACAG;gBACAE;gBACAkB;cACA;YACA;cACA;cACApB;gBACAE;gBACAkB;cACA;YACA;YACA;UACA;UACAZ;YACAX;YACA;YACA;UACA;QACA;MACA;IACA;IACA0B;MAAA;MACAvB;QACAM;QACAL;UACA;UACA;YACA;cAAA,OACAuB;YAAA,EACA;YACA;UACA;YACA;UACA;UACA3B;QACA;QACAW;UACAX;UACA;UACAG;YACAE;YACAkB;UACA;QACA;MACA;IACA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACA/C;kBACAG;kBACA6C;gBACA;cAAA;gBAJAC;gBAKA;kBACA5B;oBACAoB;oBACAlB;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA2B;gBACAhC;gBACAG;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAQA;IACA8B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAF;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA5B;kBACAoB;kBACAlB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA6B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAlC;kBACA;gBACA;gBAAA;gBAEA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBACAG;kBACAoB;kBACAlB;gBACA;gBAAA;cAAA;gBAGA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAF;kBACAoB;kBACAlB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA8B;MACA;QACAhC;QACAA;UACAkB;QACA;MACA;MACA;MACA;IACA;IACAe;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjC;kBACAE;gBACA;gBAAA;gBAEAgC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAEA;kBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;kBACApC;oBACAqC;oBACApC;oBACAO;kBACA;gBACA;cAAA;gBANA0B;gBAOA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACAlC;kBACAoB;kBACAlB;gBACA;cAAA;gBAAA;gBAAA;gBAAA,OAIA;kBACAF;oBACAkB;oBACAjB;oBACAO;kBACA;gBACA;cAAA;gBANA8B;gBAOA;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;gBACAtC;kBACAoB;kBACAlB;gBACA;cAAA;gBAAA;gBAAA,OAGA;kBACAvB;kBACAG;kBACA6C;kBACAY;kBACAC;gBACA;cAAA;gBANAZ;gBAOA/B;gBACA;kBACAG;oBACAoB;oBACAlB;kBACA;gBACA;gBACA;gBACA2B;gBACAhC;gBACAG;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAH;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;gBAAA;gBAEAG;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAyC;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;MACA;QACA;QACA5C;UACAoB;UACAlB;UACA2C;QACA;QACA9B;UACAf;YACAkB;UACA;QACA;MACA;QACAlB;UACAoB;UACAlB;QACA;MACA;IACA;IACA4C;MACA;MACA9C;QACAkB;MACA;IACA;IACA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFApB;gBAGA;gBACA;kBAAA;gBAAA;gBACA;kBACA,iBACA,oFACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA5B;kBACAoB;kBACAlB;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA+C;MAAA;MACA;MACA;MACAlC;QACA;QACA;UACAwB;UACAC;UACAb;UACA7C;UACAH;QACA;UACA;YACA;YACAqB;cACAoB;cACAlB;YACA;YACA;UACA;UACA;UACA;YACA;UACA;YACA;UACA;QACA;UACA;UACAF;YACAoB;YACAlB;UACA;QACA;MACA;IACA;IACAgD;MAAA;MACA;MACA;QACAlD;UACAoB;UACAlB;QACA;QACA;MACA;MACA;QACAiD;QACAC;MACA;MACA;QACA;UACA;QACA;UACApD;YACAoB;YACAlB;UACA;UACA;UACA;UACAa;YACAf;cACAkB;YACA;UACA;UACA;QACA;MACA;QACAlB;UACAoB;UACAlB;QACA;QACA;MACA;IACA;IACAmD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACArD;kBACAE;gBACA;gBAAA;gBAEAoD;gBACAzD;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAG;kBACAoB;kBACAlB;gBACA;cAAA;gBAAA;gBAEAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACAuD;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAEA;gBACA;gBACA;gBACA;cACA;cACA;cACA;gBACA1D;gBACA;cACA;;cACA;gBACA;cACA;cACA;cACAA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACA2D;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cAAA;cAAA,OACA;YAAA;cACAxD;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAyD;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAEA;cACAzD;gBACAH;gBACA;gBACA;gBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChwBA;AAAA;AAAA;AAAA;AAA+lD,CAAgB,mjDAAG,EAAC,C;;;;;;;;;;;ACAnnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "shifu/Receiving.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './shifu/Receiving.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Receiving.vue?vue&type=template&id=23563916&scoped=true&\"\nvar renderjs\nimport script from \"./Receiving.vue?vue&type=script&lang=js&\"\nexport * from \"./Receiving.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Receiving.vue?vue&type=style&index=0&id=23563916&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"23563916\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"shifu/Receiving.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Receiving.vue?vue&type=template&id=23563916&scoped=true&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tag/u-tag\" */ \"uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    \"u-Input\": function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u--input/u--input\" */ \"uview-ui/components/u--input/u--input.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g1 = item.mobile.slice(0, 3)\n    return {\n      $orig: $orig,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.list.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.confirmshow = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.masterModalShow = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.detailModalShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Receiving.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Receiving.vue?vue&type=script&lang=js&\"", "\n<template>\n\t<view class=\"page\">\n\t\t<tabbar :cur=\"0\"></tabbar>\n\t\t<view class=\"img\">\n\t\t\t<u-swiper :list=\"list1\" height=\"108\"></u-swiper>\n\t\t</view>\n\t\t<view class=\"location-bar\">\n\t\t\t<view class=\"location-info\">\n\t\t\t\t<view>当前接单位置：{{ province + city + district || '定位中...' }}</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"check_box\">\n\t\t\t<view class=\"check\">\n\t\t\t\t<u-tag :text=\"currentCateName\" icon=\"arrow-down-fill\" @click=\"chooseCate\"></u-tag>\n\t\t\t\t<view class=\"cate-dropdown\" v-if=\"showCate\">\n\t\t\t\t\t<view class=\"cate-item\" v-for=\"(cate, index) in cateList\" :key=\"index\" @click=\"selectClick(cate)\">\n\t\t\t\t\t\t{{ cate.name }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"reset\">\n\t\t\t\t<u-tag text=\"重置筛选\" @click=\"reset\" plain plain-fill></u-tag>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-empty mode=\"order\" icon=\"http://cdn.uviewui.com/uview/empty/order.png\" v-if=\"list.length == 0\"></u-empty>\n\t\t<view class=\"re_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"seeDetail(item)\">\n\t\t\t<view class=\"top\">\n\t\t\t\t<image :src=\"item.goodsCover\" style=\"width: 160rpx;height: 160rpx;border-radius: 10rpx;\"></image>\n\t\t\t\t<view class=\"order\">\n\t\t\t\t\t<div class=\"title\">{{ item.goodsName }}<span v-if=\"item.type != 0\"\n\t\t\t\t\t\t\tstyle=\"font-size: 24rpx;color:#999;margin-left: 10rpx;\">(报价0.00元起)</span></div>\n\t\t\t\t\t<div class=\"price\">{{ item.type == 0 ? `￥${item.payPrice}` : '待报价' }}</div>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view @click=\"dingyue()\" class=\"info\">\n\t\t\t\t<view class=\"address\">\n\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t<u-icon name=\"map-fill\" color=\"#2979ff\" size=\"22\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right\">\n\t\t\t\t\t\t<view class=\"address_name\">{{ item.address }}</view>\n\t\t\t\t\t\t<view class=\"address_Info\">{{ item.addressInfo }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tel\">\n\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t<u-icon name=\"phone-fill\" color=\"#2979ff\" size=\"22\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right\">{{ item.mobile.slice(0, 3) + '********' }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"notes\" v-if=\"item.text != ''\">\n\t\t\t\t<view style=\"color:#999999;\">备注内容:</view>\n\t\t\t\t{{ item.text }}\n\t\t\t</view>\n\t\t\t<view class=\"btn\" :style=\"item.type == 1 ? '' : 'background-color:#2E80FE;color:#fff;'\"\n\t\t\t\************=\"seeDetail(item)\">\n\t\t\t\t{{ item.type == 1 ? '立即报价' : '立即接单' }}\n\t\t\t</view>\n\t\t</view>\n\t\t<u-popup :show=\"show\" :round=\"10\" closeable @close=\"close\">\n\t\t\t<view class=\"box\">\n\t\t\t\t<view class=\"title\">立即报价</view>\n\t\t\t\t<view class=\"title2\">报价金额</view>\n\t\t\t\t<view class=\"money\">\n\t\t\t\t\t<u--input placeholder=\"请输入报价金额\" prefixIcon=\"rmb\" prefixIconStyle=\"font-size: 22px;color: #909399\"\n\t\t\t\t\t\ttype=\"digit\" v-model=\"input\" @input=\"validateInput\" maxlength=\"10\"></u--input>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"btn\" @click=\"confirmBao\">确认报价</view>\n\t\t\t</view>\n\t\t</u-popup>\n\t\t<u-modal :show=\"confirmshow\" :content=\"content\" showCancelButton @confirm=\"confirmRe\"\n\t\t\t@cancel=\"confirmshow = false\"></u-modal>\n\n\t\t<u-modal :show=\"masterModalShow\" content=\"您还不是师傅,请去入驻\" showCancelButton @confirm=\"goToSettle\"\n\t\t\t@cancel=\"masterModalShow = false\"></u-modal>\n\n\t\t<u-modal :show=\"detailModalShow\" title=\"服务承诺\" showCancelButton cancelText=\"不同意\" confirmText=\"同意\"\n\t\t\t@confirm=\"confirmDetail\" @cancel=\"detailModalShow = false\" v-if=\"shifustutus.data !== -2 && shifustutus.data !== -1\">\n\t\t\t<view class=\"modal-content\">\n\t\t\t\t<rich-text\n\t\t\t\t\t:nodes=\"getconfigs?getconfigs:configInfo.shifuQualityCommitment\"></rich-text>\n\t\t\t</view>\n\t\t</u-modal>\n\n\t\t<view class=\"loadmore\" v-if=\"list.length >= 10\">\n\t\t\t<u-loadmore :status=\"status\" />\n\t\t</view>\n\t\t<!-- <view class=\"footer\">---------------皖ICP备2023012035号-1---------------</view> -->\n\t\t</view>\n</template>\n\n<script>\n\timport tabbar from \"@/components/tabbarsf.vue\";\n\timport {\n\t\tmapState,\n\t\tmapActions\n\t} from 'vuex';\n\texport default {\n\t\tcomponents: {\n\t\t\ttabbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderData: '',\n\t\t\t\tshowDingyue: false,\n\t\t\t\tshowCate: false,\n\t\t\t\tinfodata: '',\n\t\t\ttmplIds: [\n\t\t\t\t'',\n\t\t\t\t'',\n\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t],\n\t\t\t\tstatus: 'loadmore',\n\t\t\t\tid: '',\n\t\t\t\tshifuId: '',\n\t\t\t\tlist: [],\n\t\t\t\tshow: false,\n\t\t\t\tconfirmshow: false,\n\t\t\t\tmasterModalShow: false,\n\t\t\t\tdetailModalShow: false, // Added for new modal\n\t\t\t\tcontent: '确认接下该订单吗',\n\t\t\t\tinput: '',\n\t\t\t\tarea_id: '',\n\t\t\t\tlimit: 50,\n\t\t\t\tpage: 1,\n\t\t\t\tbannerList: [],\n\t\t\t\tconfigInfo: '',\n\t\t\t\tgetconfigs: '',\n\t\t\t\tlist1: [],\n\t\t\t\tlng: '',\n\t\t\t\tshifustutus: { // Initialize shifustutus as an object to hold 'data' and 'msg'\n\t\t\t\t\tdata: 0,\n\t\t\t\t\tmsg: ''\n\t\t\t\t},\n\t\t\t\tlat: '',\n\t\t\t\tcateList: [],\n\t\t\t\tcurrentCateId: '',\n\t\t\t\tcurrentCateName: '选择接单分类',\n\t\t\t\tcopyCateList: [],\n\t\t\t\tprovince: '',\n\t\t\t\tcity: '',\n\t\t\t\tdistrict: '',\n\t\t\t\tisPageLoaded: false, // 添加页面加载状态标识\n\t\t\t\tselectedItem: null // Added to store the selected item\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t...mapState({\n\t\t\t\tconfigInfos: (state) => state.config.configInfo,\n\t\t\t\trefreshReceiving: (state) => state.service.refreshReceiving || '', // Map Vuex state\n\t\t\t})\n\n\t\t},\n\n\t\tmethods: {\n\t\t\tdingyue() {\n\t\t\t\tconsole.log('dingyue called');\n\t\t\t\tconst allTmplIds = this.tmplIds;\n\t\t\t\tif (allTmplIds.length < 3) {\n\t\t\t\t\tconsole.error(\"Not enough template IDs available:\", allTmplIds);\n\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t// \ttitle: '模板ID不足'\n\t\t\t\t\t// });\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst shuffled = [...allTmplIds].sort(() => 0.5 - Math.random());\n\t\t\t\tconst selectedTmplIds = shuffled.slice(0, 3);\n\t\t\t\tconsole.log(\"Selected template IDs:\", selectedTmplIds);\n\t\t\t\tconst templateData = selectedTmplIds.map((id, index) => ({\n\t\t\t\t\ttemplateId: id,\n\t\t\t\t\ttemplateCategoryId: index === 0 ? 10 : 5\n\t\t\t\t}));\n\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\ttmplIds: selectedTmplIds,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);\n\t\t\t\t\t\t// Check if any of the template IDs were rejected\n\t\t\t\t\t\tconst hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');\n\t\t\t\t\t\tconst hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');\n\t\t\t\t\t\tif (hasRejection && !hasShownModal) {\n\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\t\t\tcontent: '您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收用户订单通知。',\n\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\tconfirmText: '去开启',\n\t\t\t\t\t\t\t\tconfirmColor: '#007AFF',\n\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasShownSubscriptionModal', true);\n\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\tuni.openSetting({\n\t\t\t\t\t\t\t\t\t\t\twithSubscriptions: true\n\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t} else if (modalRes.cancel) {\n\t\t\t\t\t\t\t\t\t\tuni.setStorageSync('hasCanceledSubscription', true);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.templateCategoryIds = [];\n\t\t\t\t\t\tselectedTmplIds.forEach((templId, index) => {\n\t\t\t\t\t\t\tconsole.log(`Template ${templId} status: ${res[templId]}`);\n\t\t\t\t\t\t\tif (res[templId] === 'accept') {\n\t\t\t\t\t\t\t\tconst templateCategoryId = templateData[index].templateCategoryId;\n\t\t\t\t\t\t\t\tif (templateCategoryId === 10) {\n\t\t\t\t\t\t\t\t\tfor (let i = 0; i < 15; i++) {\n\t\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.templateCategoryIds.push(templateCategoryId);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tconsole.log('Accepted message push for template:', templId);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log('Updated templateCategoryIds:', this.templateCategoryIds);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('requestSubscribeMessage failed:', err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\n\t\t\t// 输入验证方法\n\t\t\tvalidateInput(e) {\n\t\t\t\tlet value = e.detail ? e.detail.value : e;\n\n\t\t\t\t// 移除所有中文字符\n\t\t\t\tvalue = value.replace(/[\\u4e00-\\u9fa5]/g, '');\n\n\t\t\t\t// 只允许数字和小数点\n\t\t\t\tvalue = value.replace(/[^\\d.]/g, '');\n\n\t\t\t\t// 处理小数点逻辑\n\t\t\t\tconst parts = value.split('.');\n\t\t\t\tif (parts.length > 2) {\n\t\t\t\t\t// 如果有多个小数点，只保留第一个\n\t\t\t\t\tvalue = parts[0] + '.' + parts.slice(1).join('');\n\t\t\t\t}\n\n\t\t\t\tif (parts.length === 2) {\n\t\t\t\t\t// 如果有小数部分，限制小数点后只能有两位\n\t\t\t\t\tif (parts[1].length > 2) {\n\t\t\t\t\t\tparts[1] = parts[1].substring(0, 2);\n\t\t\t\t\t\tvalue = parts[0] + '.' + parts[1];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 防止以多个0开头（除了0.开头的情况）\n\t\t\t\tif (value.length > 1 && value.charAt(0) === '0' && value.charAt(1) !== '.') {\n\t\t\t\t\tvalue = value.substring(1);\n\t\t\t\t}\n\n\t\t\t\t// 更新input值\n\t\t\t\tthis.input = value;\n\t\t\t},\n\n\t\t\treset() {\n\t\t\t\tthis.currentCateName = '选择接单分类';\n\t\t\t\tthis.currentCateId = '';\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.getList();\n\t\t\t},\n\t\t\ttextsss() {\n\t\t\t\tif (this.infodata.status === 2) {\n\t\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\t\ttmplIds: this.tmplIds,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage result:', res);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage failed:', err);\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tcloseCate() {\n\t\t\t\tthis.showCate = false;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.cateList = this.copyCateList;\n\t\t\t\t}, 500);\n\t\t\t},\n\t\t\tchooseCate() {\n\t\t\t\tthis.showCate = !this.showCate;\n\t\t\t},\n\t\t\tasyncChange() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '订阅提示',\n\t\t\t\t\tcontent: '请手动开启消息订阅以获取订单状态提醒',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/shifu/userProfile'\n\t\t\t\t\t\t})\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t// console.log('uni.showModal failed:', err);\n\t\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t\t// \ttitle: '显示订阅提示失败',\n\t\t\t\t\t\t// \ticon: 'none'\n\t\t\t\t\t\t// });\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tconfirmSubscription() {\n\t\t\t\tuni.openSetting({\n\t\t\t\t\twithSubscriptions: true,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tconsole.log('uni.openSetting success');\n\t\t\t\t\t\tuni.getSetting({\n\t\t\t\t\t\t\twithSubscriptions: true,\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tconst subscriptions = res.subscriptionsSetting;\n\t\t\t\t\t\t\t\tconsole.log('uni.getSetting result:', subscriptions);\n\t\t\t\t\t\t\t\tif (subscriptions.mainSwitch) {\n\t\t\t\t\t\t\t\t\tthis.requestSubscription();\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.showDingyue = true;\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '订阅取消',\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tthis.checkSubscriptionStatus();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\t\tconsole.log('uni.getSetting failed:', err);\n\t\t\t\t\t\t\t\tthis.showDingyue = true;\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '获取设置失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.log('uni.openSetting failed:', err);\n\t\t\t\t\t\tthis.showDingyue = true;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '打开设置失败，请稍后重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tcancelSubscription() {\n\t\t\t\tthis.checkSubscriptionStatus();\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '订阅取消',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t},\n\t\t\trequestSubscription() {\n\t\t\t\tlet infodata = JSON.parse(uni.getStorageSync('shiInfo'));\n\t\t\t\tconsole.log(infodata);\n\t\t\t\tif (infodata.status === 2) {\n\t\t\t\t\tuni.requestSubscribeMessage({\n\t\t\t\t\t\ttmplIds: this.tmplIds,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage result:', res);\n\t\t\t\t\t\t\tconst anyAccepted = this.tmplIds.some(id => res[id] === 'accept');\n\t\t\t\t\t\t\tif (anyAccepted) {\n\t\t\t\t\t\t\t\tthis.showDingyue = false;\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '消息订阅成功',\n\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.showDingyue = true;\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '订阅取消',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.checkSubscriptionStatus();\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.log('requestSubscribeMessage failed:', err);\n\t\t\t\t\t\t\tthis.showDingyue = true;\n\t\t\t\t\t\t\tthis.checkSubscriptionStatus();\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tcheckSubscriptionStatus() {\n\t\t\t\tuni.getSetting({\n\t\t\t\t\twithSubscriptions: true,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst subscriptions = res.subscriptionsSetting;\n\t\t\t\t\t\tif (subscriptions.mainSwitch) {\n\t\t\t\t\t\t\tconst anySubscribed = this.tmplIds.some(id =>\n\t\t\t\t\t\t\t\tsubscriptions.itemSettings && subscriptions.itemSettings[id] === 'accept'\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tthis.showDingyue = !anySubscribed;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.showDingyue = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconsole.log('Updated showDingyue:', this.showDingyue);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.log('checkSubscriptionStatus failed:', err);\n\t\t\t\t\t\tthis.showDingyue = true;\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '检查订阅状态失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync selectClick(cate) {\n\t\t\t\tif (cate.id) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst res = await this.$api.shifu.indexQuote({\n\t\t\t\t\t\t\tlng: this.lng,\n\t\t\t\t\t\t\tlat: this.lat,\n\t\t\t\t\t\t\tparentId: cate.id\n\t\t\t\t\t\t});\n\t\t\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t\t}, 3000);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.showCate = false;\n\t\t\t\t\t\tthis.currentCateName = cate.name;\n\t\t\t\t\t\tthis.currentCateId = cate.id;\n\t\t\t\t\t\tthis.list = res.data.list || [];\n\n\t\t\t\t\t\tlet count = this.list.length;\n\t\t\t\t\t\tconsole.log('List count:', count);\n\t\t\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t\t// \ttitle: '获取分类订单失败'\n\t\t\t\t\t\t// });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync getCate() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.serviceCate();\n\t\t\t\t\tthis.cateList = res || [];\n\t\t\t\t\tthis.copyCateList = res || [];\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '获取分类失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync seeDetail(item) {\n\t\t\t\tthis.$api.shifu.getshifstutas().then(res => {\n\t\t\t\t\tconsole.log(\"shifustutus response:\", res);\n\t\t\t\t\tthis.shifustutus = res; // Assign the whole response object\n\t\t\t\t});\n\t\t\t\ttry {\n\t\t\t\t\tthis.selectedItem = item; // Store the selected item\n\t\t\t\t\tif (this.shifustutus.data === -2) {\n\t\t\t\t\t\t// If shifustutus.data is -2, show the \"成为师傅才能操作\" modal\n\t\t\t\t\t\tthis.masterModalShow = true;\n\t\t\t\t\t} else if (this.shifustutus.data === -1) {\n\t\t\t\t\t\t// If shifustutus.data is -1, show the message from shifustutus.msg\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: this.shifustutus.msg || '无法进行此操作'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// For other shifustutus.data values, show the \"服务承诺\" modal\n\t\t\t\t\t\tthis.detailModalShow = true;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '检查身份失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tconfirmDetail() {\n\t\t\t\tif (this.selectedItem) {\n\t\t\t\t\tuni.setStorageSync('selectedOrder', this.selectedItem);\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/shifu/master_order_details?id=${this.selectedItem.id}`\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthis.detailModalShow = false;\n\t\t\t\tthis.selectedItem = null;\n\t\t\t},\n\t\t\tasync getList() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中'\n\t\t\t\t});\n\t\t\t\ttry {\n\t\t\t\t\tlet location = {\n\t\t\t\t\t\tlongitude: '',\n\t\t\t\t\t\tlatitude: ''\n\t\t\t\t\t};\n\t\t\t\t\ttry {\n\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 500));\n\t\t\t\t\t\tlocation = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.getLocation({\n\t\t\t\t\t\t\t\ttype: 'gcj02',\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.lng = location.longitude;\n\t\t\t\t\t\tthis.lat = location.latitude;\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tthis.lng = '115.259956';\n\t\t\t\t\t\tthis.lat = '33.066271';\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '定位失败，使用默认位置'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst geoRes = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.request({\n\t\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?location=${this.lng},${this.lat}&key=2fb9ec1a184338e3cce567b7d2bab08f`,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.province = geoRes.data.regeocode.addressComponent.province || '';\n\t\t\t\t\t\tthis.city = geoRes.data.regeocode.addressComponent.city || '';\n\t\t\t\t\t\tthis.district = geoRes.data.regeocode.addressComponent.district || '';\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tthis.province = '安徽省';\n\t\t\t\t\t\tthis.city = '阜阳市';\n\t\t\t\t\t\tthis.district = '临泉县';\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '地址解析失败，使用默认地址'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\tconst res = await this.$api.shifu.indexQuote({\n\t\t\t\t\t\tlng: this.lng,\n\t\t\t\t\t\tlat: this.lat,\n\t\t\t\t\t\tparentId: 0,\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t}, 3000);\n\t\t\t\t\t}\n\t\t\t\t\tthis.$set(this, 'list', res.data.list || []);\n\t\t\t\t\tlet count = this.list.length;\n\t\t\t\t\tconsole.log('List count:', count);\n\t\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.log(error)\n\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t// \ttitle: '获取订单列表失败'\n\t\t\t\t\t// });\n\t\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleReceive(item) {\n\t\t\t\tthis.textsss();\n\t\t\t\tthis.orderData = item;\n\t\t\t\tthis.id = item.id;\n\t\t\t\tif (item.type == 0) {\n\t\t\t\t\tthis.confirmshow = true;\n\t\t\t\t} else {\n\t\t\t\t\tthis.show = true;\n\t\t\t\t}\n\t\t\t},\n\t\t\tclose() {\n\t\t\t\tthis.show = false;\n\t\t\t\tthis.input = '';\n\t\t\t},\n\t\t\tconfirmRe() {\n\t\t\t\tthis.confirmshow = false;\n\t\t\t\tthis.$api.shifu.rece_Order({\n\t\t\t\t\torder_id: this.id\n\t\t\t\t}).then(res => {\n\t\t\t\t\tthis.getList();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '接单成功',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/shifu/master_my_order'\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 1000);\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'fail',\n\t\t\t\t\t\ttitle: error.message || '接单失败'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoToSettle() {\n\t\t\t\tthis.masterModalShow = false;\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/shifu/Settle'\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync getServiceInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.index({\n\t\t\t\t\t\tcity_id: this.area_id\n\t\t\t\t\t});\n\t\t\t\t\tthis.bannerList = res.data || [];\n\t\t\t\t\tthis.list1 = res.data.map(item => item.img) || [];\n\t\t\t\t\tif (!this.list1.length) {\n\t\t\t\t\t\tthis.list1 = [\n\t\t\t\t\t\t\t'https://zskj.asia/attachment/image/666/24/09/2bdd13fab41b42b987bcfc501aa535bb.jpg'\n\t\t\t\t\t\t];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '获取轮播图失败'\n\t\t\t\t\t});\n\t\t\t\t\tthis.list1 = ['https://zskj.asia/attachment/image/666/24/09/e790eea3f21b4f48ab2b00b034468035.jpg'];\n\t\t\t\t}\n\t\t\t},\n\t\t\tonReachBottom() {\n\t\t\t\tif (this.status == 'nomore') return;\n\t\t\t\tthis.status = 'loading';\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.page++;\n\t\t\t\t\tthis.$api.shifu.indexQuote({\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: 20,\n\t\t\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\t\t\tlat: this.lat,\n\t\t\t\t\t\tlng: this.lng,\n\t\t\t\t\t}).then(res => {\n\t\t\t\t\t\tif (!res.data || !res.data.list || res.data.list.length === 0) {\n\t\t\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\ttitle: '没有更多数据'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.$set(this, 'list', [...this.list, ...(res.data.list || [])]);\n\t\t\t\t\t\tif (res.data.list.length < 10) {\n\t\t\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.status = 'loadmore';\n\t\t\t\t\t\t}\n\t\t\t\t\t}).catch(error => {\n\t\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '加载失败，请稍后重试'\n\t\t\t\t\t\t});\n\t\t\t\t\t});\n\t\t\t\t}, 1000);\n\t\t\t},\n\t\t\tconfirmBao() {\n\t\t\t\tthis.textsss();\n\t\t\t\tif (this.input == '' || this.input == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '请输入报价（不能为0哦）'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tconst updatedOrderData = {\n\t\t\t\t\torderId: this.id,\n\t\t\t\t\tprice: this.input\n\t\t\t\t};\n\t\t\t\tthis.$api.shifu.updateBao(updatedOrderData).then(res => {\n\t\t\t\t\tif (res === -1) {\n\t\t\t\t\t\tthis.masterModalShow = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\t\ttitle: '报价成功'\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.getList();\n\t\t\t\t\t\tthis.close();\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.redirectTo({\n\t\t\t\t\t\t\t\turl: '/shifu/master_bao_list'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}, 1000);\n\t\t\t\t\t\tthis.getList();\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'fail',\n\t\t\t\t\t\ttitle: error.message || '报价失败'\n\t\t\t\t\t});\n\t\t\t\t\tthis.close();\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync initializePage() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '初始化中'\n\t\t\t\t});\n\t\t\t\ttry {\n\t\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\n\t\t\t\t\tconsole.log('Platform:', systemInfo.platform);\n\t\t\t\t\tawait this.getServiceInfo();\n\t\t\t\t\tawait this.getCate();\n\t\t\t\t\tawait this.getList();\n\t\t\t\t\tawait this.checkSubscriptionStatus();\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '初始化失败，请稍后重试'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tasync onLoad() {\n\n\t\t\tthis.$api.base.getConfig().then(res => {\n\t\t\t\t// console.log(res)\n\t\t\t\tthis.getconfigs = res.shifuQualityCommitment\n\t\t\t\t// console.log(this.getconfigs)\n\t\t\t})\n\t\t\t// Modified assignment for shifustutus\n\t\t\tthis.$api.shifu.getshifstutas().then(res => {\n\t\t\t\tconsole.log(\"shifustutus response:\", res);\n\t\t\t\tthis.shifustutus = res; // Assign the whole response object\n\t\t\t});\n\t\t\tif (uni.getStorageSync('shiInfo')) {\n\t\t\t\tthis.infodata = JSON.parse(uni.getStorageSync('shiInfo'));\n\t\t\t}\n\t\t\tthis.configInfo = uni.getStorageSync('configInfo');\n\t\t\tconsole.log(this.infodata);\n\t\t\tthis.isPageLoaded = true; // 标记页面已初始化\n\t\t\tawait this.initializePage();\n\n\t\t},\n\t\tasync onPullDownRefresh() {\n\t\t\tthis.page = 1;\n\t\t\tthis.list = [];\n\t\t\tawait this.getList();\n\t\t\tuni.stopPullDownRefresh();\n\t\t},\n\t\tasync onShow() {\n\n\t\t\tthis.checkSubscriptionStatus();\n\t\t\tuni.$on('refreshReceivingList', () => {\n\t\t\t\tconsole.log('接收到刷新通知，正在重新加载订单列表...');\n\t\t\t\t// 这里可以加上一些交互提示，比如显示一个 loading\n\t\t\t\t// 然后立即调用获取数据的方法\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.getList();\n\t\t\t});\n\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tmin-height: 100vh;\n\t\toverflow: auto;\n\t\tbackground-color: #f3f4f5;\n\t\tpadding-bottom: 120rpx;\n\n\t\t.img {\n\t\t\twidth: 690rpx;\n\t\t\tmargin: 20rpx auto;\n\t\t}\n\n\t\t.location-bar {\n\t\t\tdisplay: flex;\n\t\t\tpadding: 20rpx;\n\t\t\tborder: 1rpx solid #eeeeee;\n\t\t\tcolor: #999;\n\t\t\tfont-size: 28rpx;\n\t\t}\n\n\t\t.subscription {\n\t\t\tflex-shrink: 0;\n\t\t}\n\n\t\t.location-info {\n\t\t\tmargin-left: auto;\n\t\t\ttext-align: right;\n\t\t}\n\n\t\t.check_box {\n\t\t\tmargin: 20rpx auto;\n\t\t\twidth: 690rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\tposition: relative;\n\n\t\t\t.check {\n\t\t\t\twidth: 500rpx;\n\t\t\t\tposition: relative;\n\t\t\t}\n\n\t\t\t.reset {\n\t\t\t\twidth: 160rpx;\n\t\t\t}\n\n\t\t\t.cate-dropdown {\n\t\t\t\tposition: absolute;\n\t\t\t\ttop: 80rpx;\n\t\t\t\tleft: 0;\n\t\t\t\twidth: 500rpx;\n\t\t\t\tmax-height: 400rpx;\n\t\t\t\toverflow-y: auto;\n\t\t\t\tbackground-color: #fff;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t\t\t\tz-index: 1000;\n\t\t\t}\n\n\t\t\t.cate-item {\n\t\t\t\tpadding: 20rpx 30rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tborder-bottom: 1rpx solid #f0f0f0;\n\n\t\t\t\t&:last-child {\n\t\t\t\t\tborder-bottom: none;\n\t\t\t\t}\n\n\t\t\t\t&:hover {\n\t\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.box {\n\t\t\tpadding: 40rpx 30rpx;\n\n\t\t\t.title {\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #171717;\n\t\t\t}\n\n\t\t\t.title2 {\n\t\t\t\tmargin-top: 32rpx;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #171717;\n\t\t\t}\n\n\t\t\t.btn {\n\t\t\t\tmargin: 0 auto;\n\t\t\t\tmargin-top: 42rpx;\n\t\t\t\twidth: 688rpx;\n\t\t\t\theight: 98rpx;\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tline-height: 98rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t}\n\t\t}\n\n\t\t.modal-content {\n\t\t\tpadding: 20rpx;\n\t\t\tmax-height: 400rpx;\n\t\t\toverflow-y: auto;\n\t\t}\n\n\t\t.re_item {\n\t\t\twidth: 690rpx;\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 20rpx auto;\n\t\t\tpadding: 40rpx;\n\n\t\t\t.top {\n\t\t\t\tdisplay: flex;\n\n\t\t\t\timage {\n\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t}\n\n\t\t\t\t.order {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tjustify-content: space-between;\n\n\t\t\t\t\t.title {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #171717;\n\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\n\t\t\t\t\t.price {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.info {\n\t\t\t\tmargin-top: 40rpx;\n\n\t\t\t\t.address {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.left {\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.right {\n\t\t\t\t\t\t.address_name {\n\t\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.address_info {\n\t\t\t\t\t\t\tmargin-top: 12rpx;\n\t\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.tel {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.left {\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.right {\n\t\t\t\t\t\tfont-size: 40rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\tmax-width: 500rpx;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.notes {\n\t\t\t\tbackground-color: #f2f3f4;\n\t\t\t\tborder-radius: 5rpx;\n\t\t\t\tpadding: 10rpx;\n\t\t\t}\n\n\t\t\t.btn {\n\t\t\t\tmargin: 0 auto;\n\t\t\t\tmargin-top: 40rpx;\n\t\t\t\twidth: 610rpx;\n\t\t\t\theight: 82rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\tline-height: 82rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t}\n\t\t}\n\n\t\t.loadmore {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t.footer {\n\t\t\tcolor: #333;\n\t\t\tmargin: 20rpx 0;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 24rpx;\n\t\t}\n\t}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Receiving.vue?vue&type=style&index=0&id=23563916&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Receiving.vue?vue&type=style&index=0&id=23563916&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755908204371\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}