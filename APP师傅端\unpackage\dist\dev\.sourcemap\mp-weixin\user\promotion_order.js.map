{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion_order.vue?404e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion_order.vue?7b3a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion_order.vue?8c35", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion_order.vue?35e5", "uni-app:///user/promotion_order.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion_order.vue?ce54", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/user/promotion_order.vue?a0b1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "page", "list", "limit", "status", "currentTab", "onPullDownRefresh", "console", "setTimeout", "uni", "methods", "switchTab", "getList", "type", "pageNum", "pageSize", "onReachBottom", "onLoad"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,wBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA81B,CAAgB,82BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2Cl3B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACA;IACA;IACA;IACA;IACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACAC;QACAC;MACA;QACAR;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACAA;QACA;MACA;IACA;EACA;EACAS;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAqmD,CAAgB,yjDAAG,EAAC,C;;;;;;;;;;;ACAznD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/promotion_order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/promotion_order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./promotion_order.vue?vue&type=template&id=2b3ca7fc&scoped=true&\"\nvar renderjs\nimport script from \"./promotion_order.vue?vue&type=script&lang=js&\"\nexport * from \"./promotion_order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./promotion_order.vue?vue&type=style&index=0&id=2b3ca7fc&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2b3ca7fc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/promotion_order.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion_order.vue?vue&type=template&id=2b3ca7fc&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion_order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion_order.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<!-- Tab switching -->\n\t\t<view class=\"tabs\">\n\t\t\t<view \n\t\t\t\tclass=\"tab_item\" \n\t\t\t\t:class=\"{ active: currentTab === 1 }\" \n\t\t\t\t@click=\"switchTab(1)\"\n\t\t\t>邀请的用户订单</view>\n\t\t\t<view \n\t\t\t\tclass=\"tab_item\" \n\t\t\t\t:class=\"{ active: currentTab === 2 }\" \n\t\t\t\t@click=\"switchTab(2)\"\n\t\t\t>邀请的师傅订单</view>\n\t\t</view>\n\t\t\n\t\t<!-- Order list -->\n\t\t<view class=\"order_item\" v-for=\"(item, index) in list\" :key=\"index\">\n\t\t\t<view class=\"top\">\n\t\t\t\t<view class=\"top_left\">{{ item.createTime }}</view>\n\t\t\t\t<view class=\"top_right\" v-if=\"item.payType == 7\">交易成功</view>\n\t\t\t\t<view class=\"top_right\" v-if=\"item.payType == -1\">已取消</view>\n\t\t\t\t<view class=\"top_right\" v-if=\"item.payType == 1\">待支付</view>\n\t\t\t\t<view class=\"top_right\" v-if=\"item.payType == 3\">待上门</view>\n\t\t\t\t<view class=\"top_right\" v-if=\"item.payType == 5\">待服务</view>\n\t\t\t\t<view class=\"top_right\" v-if=\"item.payType == 6\">服务中</view>\n\t\t\t</view>\n\t\t\t<view class=\"mid\">\n\t\t\t\t<view class=\"mid_left\">{{ item.goodsName }}</view>\n\t\t\t\n\t\t\t\t<view v-if=\"currentTab === 2\" class=\"mid_right\">￥{{ item.coachServicePrice }}</view>\n\t\t\t\t<view v-else class=\"mid_right\">￥{{ item.payPrice }}</view>\n\t\t\t</view>\n\t\t\t<view v-if=\"currentTab === 1\" class=\"bottom\">用户名：{{ item.nickName ?item.nickName :item.phone }}</view>\n\t\t\t<view v-if=\"currentTab === 2\" class=\"bottom\">师傅名：{{ item.nickName ?item.nickName:item.phone}}</view>\n\t\t\t<view class=\"bottom\">订单号：{{ item.orderCode }}</view>\n\t\t\t<view class=\"blue\"></view>\n\t\t</view>\n\t\t<u-loadmore :status=\"status\" />\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tpage: 1,\n\t\t\tlist: [],\n\t\t\tlimit: 10,\n\t\t\tstatus: 'loadmore',\n\t\t\tcurrentTab: 1 // 1 for user, 2 for coach\n\t\t}\n\t},\n\tonPullDownRefresh() {\n\t\tconsole.log('refresh')\n\t\tthis.page = 1\n\t\tthis.list = []\n\t\tthis.status = 'loadmore'\n\t\tthis.getList()\n\t\tsetTimeout(() => {\n\t\t\tuni.stopPullDownRefresh()\n\t\t}, 1000)\n\t},\n\tmethods: {\n\t\t// Switch between user and coach orders\n\t\tswitchTab(tab) {\n\t\t\tif (this.currentTab !== tab) {\n\t\t\t\tthis.currentTab = tab\n\t\t\t\tthis.page = 1\n\t\t\t\tthis.list = []\n\t\t\t\tthis.status = 'loadmore'\n\t\t\t\tthis.getList(tab)\n\t\t\t}\n\t\t},\n\t\tgetList(tab) {\n\t\t\t// Modified API call to include isUserOrCoach parameter\n\t\t\tthis.$api.service.getPromoterOrders({\n\t\t\t\ttype: this.currentTab,\n\t\t\t\tpageNum: this.page,\n\t\t\t\tpageSize: this.limit,\n\t\t\t}).then(res => {\n\t\t\t\tconsole.log(res)\n\t\t\t\tthis.list = [...this.list, ...res.data.list]\n\t\t\t\tif (res.list.length < this.limit) {\n\t\t\t\t\tthis.status = 'nomore'\n\t\t\t\t} else {\n\t\t\t\t\tthis.status = 'loadmore'\n\t\t\t\t}\n\t\t\t}).catch(err => {\n\t\t\t\tconsole.error('API error:', err)\n\t\t\t\tthis.status = 'nomore'\n\t\t\t})\n\t\t}\n\t},\n\tonReachBottom() {\n\t\tif (this.status === 'nomore') return\n\t\tthis.status = 'loading'\n\t\tthis.page++\n\t\tthis.getList()\n\t},\n\tonLoad() {\n\t\tthis.getList()\n\t}\n}\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\tmin-height: 100vh;\n\tbackground: #f8f8f8;\n\tpadding: 30rpx;\n\t\n\t/* Tab styles */\n\t.tabs {\n\t\tdisplay: flex;\n\t\tbackground: #fff;\n\t\tborder-radius: 20rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tpadding: 10rpx;\n\t\t\n\t\t.tab_item {\n\t\t\tflex: 1;\n\t\t\ttext-align: center;\n\t\t\tpadding: 20rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #666;\n\t\t\tborder-radius: 16rpx;\n\t\t\ttransition: all 0.3s;\n\t\t\t\n\t\t\t&.active {\n\t\t\t\tbackground: #2E80FE;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.order_item {\n\t\twidth: 690rpx;\n\t\tbackground: #FFFFFF;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 22rpx 30rpx;\n\t\tposition: relative;\n\t\tmargin-bottom: 20rpx;\n\t\t\n\t\t.top {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\t\n\t\t\t.top_left {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #999999;\n\t\t\t}\n\t\t\t.top_right {\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tcolor: #07C160;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.mid {\n\t\t\tmargin-top: 20rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\t\n\t\t\t.mid_left {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #171717;\n\t\t\t\tmax-width: 450rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t}\n\t\t\t.mid_right {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #171717;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.bottom {\n\t\t\tmargin-top: 20rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #999999;\n\t\t}\n\t\t\n\t\t.blue {\n\t\t\twidth: 10rpx;\n\t\t\theight: 24rpx;\n\t\t\tbackground: #2E80FE;\n\t\t\tposition: absolute;\n\t\t\ttop: 86rpx;\n\t\t\tleft: 0;\n\t\t}\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion_order.vue?vue&type=style&index=0&id=2b3ca7fc&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion_order.vue?vue&type=style&index=0&id=2b3ca7fc&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755907415591\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}