{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-text/u-text.vue?c243", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-text/u-text.vue?0208", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-text/u-text.vue?d13d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-text/u-text.vue?6087", "uni-app:///node_modules/uview-ui/components/u-text/u-text.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-text/u-text.vue?9bc3", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-text/u-text.vue?ec40"], "names": ["name", "mixins", "computed", "valueStyle", "textDecoration", "fontWeight", "wordWrap", "fontSize", "style", "isNvue", "isMp", "mp", "data", "methods", "clickHandler", "uni", "phoneNumber"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,sRAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwEz2B;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA,eA4BA;EACAA;EAEAC;EAKAC;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA,oBACAC;MACA;MACA;IACA;IACAC;MACA;MAIA;IACA;IACAC;MACA;MAEAC;MAEA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACAC;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-text/u-text.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-text.vue?vue&type=template&id=15831087&scoped=true&\"\nvar renderjs\nimport script from \"./u-text.vue?vue&type=script&lang=js&\"\nexport * from \"./u-text.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-text.vue?vue&type=style&index=0&id=15831087&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"15831087\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-text/u-text.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=template&id=15831087&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLink: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-link/u-link\" */ \"uview-ui/components/u-link/u-link.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 =\n    _vm.show && _vm.mode === \"price\" ? _vm.__get_style([_vm.valueStyle]) : null\n  var g0 = _vm.show && _vm.prefixIcon ? _vm.$u.addStyle(_vm.iconStyle) : null\n  var s1 =\n    _vm.show && !(_vm.mode === \"link\") && _vm.openType && _vm.isMp\n      ? _vm.__get_style([_vm.valueStyle])\n      : null\n  var s2 =\n    _vm.show && !(_vm.mode === \"link\") && !(_vm.openType && _vm.isMp)\n      ? _vm.__get_style([_vm.valueStyle])\n      : null\n  var g1 = _vm.show && _vm.suffixIcon ? _vm.$u.addStyle(_vm.iconStyle) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        s1: s1,\n        s2: s2,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=script&lang=js&\"", "<template>\n    <view\n        class=\"u-text\"\n        :class=\"[]\"\n        v-if=\"show\"\n        :style=\"{\n            margin: margin,\n\t\t\tjustifyContent: align === 'left' ? 'flex-start' : align === 'center' ? 'center' : 'flex-end'\n        }\"\n        @tap=\"clickHandler\"\n    >\n        <text\n            :class=\"['u-text__price', type && `u-text__value--${type}`]\"\n            v-if=\"mode === 'price'\"\n            :style=\"[valueStyle]\"\n            >￥</text\n        >\n        <view class=\"u-text__prefix-icon\" v-if=\"prefixIcon\">\n            <u-icon\n                :name=\"prefixIcon\"\n                :customStyle=\"$u.addStyle(iconStyle)\"\n            ></u-icon>\n        </view>\n        <u-link\n            v-if=\"mode === 'link'\"\n            :text=\"value\"\n            :href=\"href\"\n            underLine\n        ></u-link>\n        <template v-else-if=\"openType && isMp\">\n            <button\n                class=\"u-reset-button u-text__value\"\n                :style=\"[valueStyle]\"\n                :data-index=\"index\"\n                :openType=\"openType\"\n                @getuserinfo=\"onGetUserInfo\"\n                @contact=\"onContact\"\n                @getphonenumber=\"onGetPhoneNumber\"\n                @error=\"onError\"\n                @launchapp=\"onLaunchApp\"\n                @opensetting=\"onOpenSetting\"\n                :lang=\"lang\"\n                :session-from=\"sessionFrom\"\n                :send-message-title=\"sendMessageTitle\"\n                :send-message-path=\"sendMessagePath\"\n                :send-message-img=\"sendMessageImg\"\n                :show-message-card=\"showMessageCard\"\n                :app-parameter=\"appParameter\"\n            >\n                {{ value }}\n            </button>\n        </template>\n        <text\n            v-else\n            class=\"u-text__value\"\n            :style=\"[valueStyle]\"\n            :class=\"[\n                type && `u-text__value--${type}`,\n                lines && `u-line-${lines}`\n            ]\"\n            >{{ value }}</text\n        >\n        <view class=\"u-text__suffix-icon\" v-if=\"suffixIcon\">\n            <u-icon\n                :name=\"suffixIcon\"\n                :customStyle=\"$u.addStyle(iconStyle)\"\n            ></u-icon>\n        </view>\n    </view>\n</template>\n\n<script>\nimport value from './value.js'\nimport button from '../../libs/mixin/button.js'\nimport openType from '../../libs/mixin/openType.js'\nimport props from './props.js'\n/**\n * Text 文本\n * @description 此组件集成了文本类在项目中的常用功能，包括状态，拨打电话，格式化日期，*替换，超链接...等功能。 您大可不必在使用特殊文本时自己定义，text组件几乎涵盖您能使用的大部分场景。\n * @tutorial https://www.uviewui.com/components/loading.html\n * @property {String} \t\t\t\t\ttype\t\t主题颜色\n * @property {Boolean} \t\t\t\t\tshow\t\t是否显示（默认 true ）\n * @property {String | Number}\t\t\ttext\t\t显示的值\n * @property {String}\t\t\t\t\tprefixIcon\t前置图标\n * @property {String} \t\t\t\t\tsuffixIcon\t后置图标\n * @property {String} \t\t\t\t\tmode\t\t文本处理的匹配模式 text-普通文本，price-价格，phone-手机号，name-姓名，date-日期，link-超链接\n * @property {String} \t\t\t\t\thref\t\tmode=link下，配置的链接\n * @property {String | Function} \t\tformat\t\t格式化规则\n * @property {Boolean} \t\t\t\t\tcall\t\tmode=phone时，点击文本是否拨打电话（默认 false ）\n * @property {String} \t\t\t\t\topenType\t小程序的打开方式\n * @property {Boolean} \t\t\t\t\tbold\t\t是否粗体，默认normal（默认 false ）\n * @property {Boolean} \t\t\t\t\tblock\t\t是否块状（默认 false ）\n * @property {String | Number} \t\t\tlines\t\t文本显示的行数，如果设置，超出此行数，将会显示省略号\n * @property {String} \t\t\t\t\tcolor\t\t文本颜色（默认 '#303133' ）\n * @property {String | Number} \t\t\tsize\t\t字体大小（默认 15 ）\n * @property {Object | String} \t\t\ticonStyle\t图标的样式 （默认 {fontSize: '15px'} ）\n * @property {String} \t\t\t\t\tdecoration\t文字装饰，下划线，中划线等，可选值 none|underline|line-through（默认 'none' ）\n * @property {Object | String | Number}\tmargin\t\t外边距，对象、字符串，数值形式均可（默认 0 ）\n * @property {String | Number} \t\t\tlineHeight\t文本行高\n * @property {String} \t\t\t\t\talign\t\t文本对齐方式，可选值left|center|right（默认 'left' ）\n * @property {String} \t\t\t\t\twordWrap\t文字换行，可选值break-word|normal|anywhere（默认 'normal' ）\n * @event {Function} click  点击触发事件\n * @example <u--text text=\"我用十年青春,赴你最后之约\"></u--text>\n */\nexport default {\n    name: 'u--text',\n    // #ifdef MP\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, value, button, openType, props],\n    // #endif\n    // #ifndef MP\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, value, props],\n    // #endif\n    computed: {\n        valueStyle() {\n            const style = {\n                textDecoration: this.decoration,\n                fontWeight: this.bold ? 'bold' : 'normal',\n                wordWrap: this.wordWrap,\n                fontSize: uni.$u.addUnit(this.size)\n            }\n            !this.type && (style.color = this.color)\n            this.isNvue && this.lines && (style.lines = this.lines)\n            this.lineHeight &&\n                (style.lineHeight = uni.$u.addUnit(this.lineHeight))\n            !this.isNvue && this.block && (style.display = 'block')\n            return uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\n        },\n        isNvue() {\n            let nvue = false\n            // #ifdef APP-NVUE\n            nvue = true\n            // #endif\n            return nvue\n        },\n        isMp() {\n            let mp = false\n            // #ifdef MP\n            mp = true\n            // #endif\n            return mp\n        }\n    },\n    data() {\n        return {}\n    },\n    methods: {\n        clickHandler() {\n            // 如果为手机号模式，拨打电话\n            if (this.call && this.mode === 'phone') {\n                uni.makePhoneCall({\n                    phoneNumber: this.text\n                })\n            }\n            this.$emit('click')\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../libs/css/components.scss';\n\n.u-text {\n    @include flex(row);\n    align-items: center;\n    flex-wrap: nowrap;\n    flex: 1;\n\t/* #ifndef APP-NVUE */\n\twidth: 100%;\n\t/* #endif */\n\n    &__price {\n        font-size: 14px;\n        color: $u-content-color;\n    }\n\n    &__value {\n        font-size: 14px;\n        @include flex;\n        color: $u-content-color;\n        flex-wrap: wrap;\n        // flex: 1;\n        text-overflow: ellipsis;\n        align-items: center;\n\n        &--primary {\n            color: $u-primary;\n        }\n\n        &--warning {\n            color: $u-warning;\n        }\n\n        &--success {\n            color: $u-success;\n        }\n\n        &--info {\n            color: $u-info;\n        }\n\n        &--error {\n            color: $u-error;\n        }\n\n        &--main {\n            color: $u-main-color;\n        }\n\n        &--content {\n            color: $u-content-color;\n        }\n\n        &--tips {\n            color: $u-tips-color;\n        }\n\n        &--light {\n            color: $u-light-color;\n        }\n    }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=style&index=0&id=15831087&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=style&index=0&id=15831087&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755908208424\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}